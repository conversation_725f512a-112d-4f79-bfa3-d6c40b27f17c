from square_tiling_counter import count_square_tilings

res = count_square_tilings(
    n=7,
    max_solutions=1,  # or set to a small int to stop early
    randomize=False,  # or True to shuffle candidate sizes a bit
    seed=0,  # only used when randomize=True
    return_one_solution=True,  # also return one example tiling if found
    time_limit_seconds=None,  # or set a number of seconds to cap the run
)

print("n:", res.n, "board:", res.board_size, "x", res.board_size)
print(
    "solutions:",
    res.solutions_count,
    "capped:",
    res.capped,
    "elapsed sec:",
    res.elapsed_sec,
)
print(
    "example tiling (r,c,size) count:",
    0 if res.one_solution is None else len(res.one_solution),
)
