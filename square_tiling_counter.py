from dataclasses import dataclass
from typing import List, Optional, Tuple
import time
import random


@dataclass
class TilingResult:
    n: int
    board_size: int
    solutions_count: int
    capped: bool
    one_solution: Optional[List[Tuple[int, int, int]]]  # (r,c,size)
    elapsed_sec: float


def count_square_tilings(
    n: int,
    max_solutions: Optional[int] = None,
    randomize: bool = False,
    seed: Optional[int] = None,
    return_one_solution: bool = True,
    time_limit_seconds: Optional[float] = None,
) -> TilingResult:
    """
    Count tilings of an SxS square (S = n(n+1)//2) by squares 1..n where there are exactly i copies of size i.
    Returns the number of distinct tilings (no overcount for identical pieces).
    """
    S = n * (n + 1) // 2
    # Quick area sanity check (always true by construction): sum i * i^2 == (sum i)^2
    required_area = sum(i * i * i for i in range(1, n + 1))
    assert required_area == S * S, "Area mismatch, check inputs."

    rows = [0] * S
    FULL_MASK = (1 << S) - 1

    remaining = {s: s for s in range(1, n + 1)}
    horiz_mask = {s: (1 << s) - 1 for s in range(1, n + 1)}
    sizes_order = list(range(n, 0, -1))

    if randomize:
        rng = random.Random(seed)
        rng.shuffle(sizes_order)
        sizes_order.sort(key=lambda x: (-x + 1e-6 * rng.random()))
    else:
        rng = None

    solutions = 0
    one_solution = None
    start_time = time.time()
    capped = False
    placements_stack = []  # (r, c, size)

    def first_empty_cell():
        for r in range(S):
            if rows[r] != FULL_MASK:
                inv = (~rows[r]) & FULL_MASK
                lsb = inv & -inv
                c = lsb.bit_length() - 1
                return r, c
        return None

    def can_place(r, c, s):
        if r + s > S or c + s > S:
            return False
        m = horiz_mask[s] << c
        for rr in range(r, r + s):
            if rows[rr] & m:
                return False
        return True

    def place(r, c, s):
        m = horiz_mask[s] << c
        for rr in range(r, r + s):
            rows[rr] |= m

    def unplace(r, c, s):
        m = horiz_mask[s] << c
        for rr in range(r, r + s):
            rows[rr] ^= m

    def dfs():
        nonlocal solutions, one_solution, capped, start_time

        if (
            time_limit_seconds is not None
            and (time.time() - start_time) >= time_limit_seconds
        ):
            capped = True
            return

        # full board?
        found = True
        for r in range(S):
            if rows[r] != FULL_MASK:
                found = False
                break
        if found:
            solutions += 1
            if one_solution is None and return_one_solution:
                one_solution = placements_stack.copy()
            if max_solutions is not None and solutions >= max_solutions:
                capped = True
            return

        # pick next empty cell (top-most, then left-most)
        for r in range(S):
            if rows[r] != FULL_MASK:
                inv = (~rows[r]) & FULL_MASK
                lsb = inv & -inv
                c = lsb.bit_length() - 1
                break

        # Try sizes that fit and remain
        if rng is None:
            candidates = [
                s for s in sizes_order if remaining[s] > 0 and can_place(r, c, s)
            ]
        else:
            candidates = [
                s for s in range(1, n + 1) if remaining[s] > 0 and can_place(r, c, s)
            ]
            rng.shuffle(candidates)
            candidates.sort(reverse=True)

        for s in candidates:
            place(r, c, s)
            remaining[s] -= 1
            placements_stack.append((r, c, s))

            dfs()
            if capped:
                placements_stack.pop()
                remaining[s] += 1
                unplace(r, c, s)
                return

            placements_stack.pop()
            remaining[s] += 1
            unplace(r, c, s)

    dfs()
    elapsed = time.time() - start_time
    return TilingResult(n, S, solutions, capped, one_solution, elapsed)
