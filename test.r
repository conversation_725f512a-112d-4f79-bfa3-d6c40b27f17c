raw <- read.table("inp.txt")
df <- as.data.frame(lapply(raw, sort))


df$diff <- abs(df[[1]] - df[[2]])
print(sum(df$diff))
t1 <- as.data.frame(table(df[[1]]))
t2 <- as.data.frame(table(df[[2]]))
names(t1)[2] <- "Freq1"
names(t2)[2] <- "Freq2"
names(t1)[1] <- "Var"
names(t2)[1] <- "Var"
t <- merge(t1, t2, all = TRUE)
t$Freq1[is.na(t$Freq1)] <- 0
t$Freq2[is.na(t$Freq2)] <- 0
print(t1)
print(t2)
print(t)
t$p2 <- t$Freq1 * t$Freq2
t$p3 <- as.numeric(as.character(t$Var)) * t$p2
#print(t)
print(sum(t$p3))